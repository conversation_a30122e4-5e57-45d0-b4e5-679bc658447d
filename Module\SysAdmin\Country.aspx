﻿<%@ Page Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="Country.aspx.cs" Inherits="Module_SysAdmin_Country" Title="ERP" Theme="Default" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" Runat="Server">

 <link type="text/css" href="../../../Css/yui-datatable.css" rel="stylesheet" />
    <link href="../../../Css/StyleSheet.css" rel="stylesheet" type="text/css" />
    <script src="../../Javascript/PopUpMsg.js" type="text/javascript"></script>
       <link href="../../Css/StyleSheet.css" rel="stylesheet" type="text/css" />
    <link href="../../Css/yui-datatable.css" rel="stylesheet" type="text/css" />
    <script src="../../../Javascript/loadingNotifier.js" type="text/javascript"></script>
    <style type="text/css">
        .style1
        {
            width: 100%;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cp3" Runat="Server">
</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="cp1" Runat="Server">
</asp:Content>
<asp:Content ID="Content4" ContentPlaceHolderID="cp2" Runat="Server">
</asp:Content>
<asp:Content ID="Content5" ContentPlaceHolderID="MainContent" Runat="Server">
</asp:Content>
<asp:Content ID="Content6" ContentPlaceHolderID="ContentPlaceHolder2" Runat="Server">
</asp:Content>
<asp:Content ID="Content7" ContentPlaceHolderID="ContentPlaceHolder3" Runat="Server">
    
  
            
    <table align="center" cellpadding="0" cellspacing="0" class="style1" width="100%">
        <tr>
            <td>
                  <table cellpadding="0" cellspacing="0" width="400px" align="center">
         
        <tr>
            <td align="left" valign="middle" style="background:url(../../images/hdbg.JPG)" height="21" class="fontcsswhite"><b>&nbsp; Country</b></td>
        </tr>
        <tr>
            <td align="center" valign="top">
                <asp:GridView ID="GridView1" 
                runat="server" 
                AllowPaging="True"
                ShowFooter="True" 
                AutoGenerateColumns="False" 
                DataKeyNames="CId" 
                FooterStyle-Wrap="True"
                OnRowDeleted="GridView1_RowDeleted" 
                DataSourceID="LocalSqlServer"
                OnRowUpdated="GridView1_RowUpdated" 
                OnRowCommand="GridView1_RowCommand" CssClass="yui-datatable-theme" Width="100%" 
                    onrowdatabound="GridView1_RowDataBound" PageSize="20">
                    
<FooterStyle Wrap="True"></FooterStyle>
                    <Columns>
                         <asp:CommandField  ButtonType="Link" ShowEditButton="True" ValidationGroup ="SG" >
                            <ItemStyle Width="3%"  HorizontalAlign="Center"/>
                        </asp:CommandField>
                         <asp:CommandField ShowDeleteButton="True"  Visible="false" ButtonType="Link" >
                             <ItemStyle Width="3%" HorizontalAlign="Center" />
                        </asp:CommandField>
                        <asp:TemplateField HeaderText="SN" Visible="false" SortExpression="Id">
                        <ItemTemplate>
                        <asp:Label ID="lblID" runat="server" Text='<%#Eval("CId") %>'>    </asp:Label>
                        </ItemTemplate>
                         <ItemStyle Width="4%" HorizontalAlign="right" /> 
                        </asp:TemplateField>
                         <asp:TemplateField HeaderText="SN">
                                        <ItemTemplate>
                                            <%# Container.DataItemIndex+1 %>
                                        </ItemTemplate>
                                        <FooterTemplate>
                        <asp:Button ID="btnInsert" runat="server" Text="Insert" CommandName="Add" 
                         OnClientClick=" return confirmationAdd()" ValidationGroup="A" CssClass="redbox" />
                        </FooterTemplate>
                                       
                                        <HeaderStyle HorizontalAlign="Center" />                                       
                                        <ItemStyle HorizontalAlign="Right" Width="4%" />
                                        <FooterStyle HorizontalAlign="Center" />
                                    </asp:TemplateField>
                        <asp:TemplateField HeaderText="Country Name" SortExpression="CountryName">
                        <ItemTemplate>
                        <asp:Label ID="lblName" runat="server" Text='<%#Eval("CountryName") %>'>    
                        </asp:Label>
                        </ItemTemplate>
                        <EditItemTemplate>
                        <asp:TextBox ID="lblName0" CssClass="box3" runat="server" Text='<%#Bind("CountryName") %>'>
                        </asp:TextBox>
                         <asp:RequiredFieldValidator ID="ReqName0" runat="server" ControlToValidate="lblName0" ValidationGroup="SG"
                           ErrorMessage="*"></asp:RequiredFieldValidator>
                        </EditItemTemplate>
                        <FooterTemplate>
                        <asp:TextBox ID="txtName" runat="server" CssClass="box3">
                        </asp:TextBox>
                         <asp:RequiredFieldValidator ID="ReqName" runat="server" ValidationGroup="A" ControlToValidate="txtName"
                           ErrorMessage="*"></asp:RequiredFieldValidator>
                        </EditItemTemplate>
                        </FooterTemplate>
                            <ItemStyle Width="30%" />
                        </asp:TemplateField>
                       
                         <asp:TemplateField HeaderText="Currency">
                         <ItemTemplate>
                        <asp:Label ID="lblCurrency" runat="server" Text='<%#Eval("Currency") %>'>    
                        </asp:Label>
                        </ItemTemplate>
                         <EditItemTemplate>
                        <asp:TextBox ID="lblCurrency0" CssClass="box3" runat="server" Text='<%#Bind("Currency") %>'>
                        </asp:TextBox>
                         <asp:RequiredFieldValidator ID="ReqCurrency0" runat="server" ControlToValidate="lblCurrency0" ValidationGroup="SG"
                           ErrorMessage="*"></asp:RequiredFieldValidator>
                        </EditItemTemplate>
                        <FooterTemplate>
                        <asp:TextBox ID="txtCurrency" runat="server" CssClass="box3">
                        </asp:TextBox>
                         <asp:RequiredFieldValidator ID="ReqCurrency" runat="server" ValidationGroup="A" ControlToValidate="txtCurrency"
                           ErrorMessage="*"></asp:RequiredFieldValidator>
                        </EditItemTemplate>
                        </FooterTemplate>
                        
                             <ItemStyle HorizontalAlign="Left" Width="5%" />
                         </asp:TemplateField>
                         
                         
                         <asp:TemplateField HeaderText="Symbol">
                         <ItemTemplate>
                        <asp:Label ID="lblSymbol" runat="server" Text='<%#Eval("Symbol") %>'>    
                        </asp:Label>
                        </ItemTemplate>
                         <EditItemTemplate>
                        <asp:TextBox ID="lblSymbol0" CssClass="box3" runat="server" Text='<%#Bind("Symbol") %>'>
                        </asp:TextBox>
                         <asp:RequiredFieldValidator ID="ReqSymbol0" runat="server" ControlToValidate="lblSymbol0" ValidationGroup="SG"
                           ErrorMessage="*"></asp:RequiredFieldValidator>
                        </EditItemTemplate>
                        <FooterTemplate>
                        <asp:TextBox ID="txtSymbol" runat="server" CssClass="box3">
                        </asp:TextBox>
                         <asp:RequiredFieldValidator ID="ReqSymbol" runat="server" ValidationGroup="A" ControlToValidate="txtSymbol"
                           ErrorMessage="*"></asp:RequiredFieldValidator>
                        </EditItemTemplate>
                        </FooterTemplate>
                        
                             <ItemStyle HorizontalAlign="Center" Width="2%" />
                         </asp:TemplateField>
                       
                    </Columns>
                    
                    <EmptyDataTemplate>
                         <asp:Button ID="btnInsert" runat="server" Text="Insert" ValidationGroup="B" CommandName="Add1" 
                         OnClientClick=" return confirmationAdd()" CssClass="redbox" />
                     <asp:TextBox ID="txtName" runat="server" CssClass="box3">
                        </asp:TextBox>
                         <asp:RequiredFieldValidator ID="ReqName" runat="server" ValidationGroup="B" ControlToValidate="txtName"
                           ErrorMessage="*"></asp:RequiredFieldValidator>
        </EmptyDataTemplate> 
                    
              </asp:GridView>
                
                <asp:SqlDataSource ID="LocalSqlServer" runat="server" 
                    ConnectionString="<%$ ConnectionStrings:LocalSqlServer %>" 
                    DeleteCommand="DELETE FROM [tblCountry] WHERE [CId] = @CId" 
                    InsertCommand="INSERT INTO [tblCountry] ([CountryName],[Currency],[Symbol]) VALUES (@CountryName,@Currency,@Symbol)" 
                    SelectCommand="SELECT * FROM [tblCountry]" 
                    
                    UpdateCommand="UPDATE [tblCountry] SET [CountryName] = @CountryName,[Currency] = @Currency,[Symbol] = @Symbol WHERE [CId] = @CId">
                    <DeleteParameters>
                        <asp:Parameter Name="CId" Type="Int32" />
                    </DeleteParameters>
                    <UpdateParameters>
                        <asp:Parameter Name="CountryName" Type="String" />
                       <asp:Parameter Name="Currency" Type="String"/>
                        <asp:Parameter Name="Symbol" Type="String"/>
                        
                        <asp:Parameter Name="CId" Type="Int32" />
                    </UpdateParameters>
                    <InsertParameters>
                        <asp:Parameter Name="CountryName" Type="String"/>
                        <asp:Parameter Name="Currency" Type="String"/>
                        <asp:Parameter Name="Symbol" Type="String"/>
                       
                    </InsertParameters>
                </asp:SqlDataSource>
                
             
                
               </td>
        </tr>
        <tr>
            <td align="center" valign="top">
                &nbsp;</td>
        </tr>
        <tr>
            <td align="center" valign="top">
                <asp:Label ID="lblMessage" runat="server"></asp:Label></td>
        </tr>
        <tr>
            <td align="center" valign="top">
                &nbsp;</td>
        </tr>
    </table></td>
        </tr>
    </table>
    
  
            
</asp:Content>
<asp:Content ID="Content8" ContentPlaceHolderID="ContentPlaceHolder4" Runat="Server">
</asp:Content>

