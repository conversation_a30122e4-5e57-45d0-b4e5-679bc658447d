---
description:
globs:
alwaysApply: false
---
# NewERP Web.sitemap Link Verification & Django Cortex Mapping

## Objective
Ensure that every URL defined in [Web.sitemap](mdc:Web.sitemap) is reachable in the running ASP.NET application (http://localhost/NewERP) and, where applicable, has a corresponding route in the Django `cortex` application (http://localhost:8000).

## Credentials
1. **NewERP**
   - Username: `sapl0002`
   - Password: `Sapl@0002`
2. **Django Cortex** (default super-user)
   - Username: `admin`
   - Password: `admin`

## Automated Testing Workflow (Playwright MCP)
1. **Login Phase**
   - Navigate to `/Login.aspx` on NewERP and authenticate with the ERP credentials.
   - Navigate to `/admin/login/` on Django Cortex and authenticate with the Django credentials.

2. **URL Extraction**
   - Parse `Web.sitemap` at runtime to build `erpUrls[]` (relative paths).
   - For each `url` in `erpUrls`, construct `fullUrl = "http://localhost/NewERP" + url.replace("~/", "/")`.

3. **ERP Navigation Test**
   - `page.goto(fullUrl)`
   - Assert `response.status() < 400`.
   - Verify the page contains a non-empty `<title>` or expected element (e.g. module breadcrumb).
   - Capture a screenshot `screenshots/erp/${slug}.png`.

4. **Cortex Mapping Test (Optional / Incremental)**
   - Maintain a JSON mapping file `link_mapping.json` with keys = ERP relative URLs and values = Cortex paths.
   - If a mapping exists for `url`, navigate to `http://localhost:8000${cortexPath}` and repeat the same assertions & screenshot capture.

5. **Reporting**
   - Aggregate results into a CSV or HTML summary showing PASS/FAIL for each ERP URL and its Cortex counterpart.

## Task Template
For every URL in `Web.sitemap`, generate a Playwright test step following this pattern:
```
# Example: System Administrator → Role Management
{
  "erp": "/Admin/Access/access_rules.aspx?ModId=1&SubModId=",
  "cortex": "/admin/auth/group/"  # Update with actual mapping if available
}
```

## Directory Conventions
- `tests/playwright/` – Playwright test specs
- `tests/playwright/link_mapping.json` – ERP → Cortex mapping
- `tests/playwright/screenshots/` – Captured screenshots
- `reports/erp_link_report.html` – Consolidated test result report

## Execution Command
From the project root, run:
```bash
npx playwright test tests/playwright/erp_link_spec.ts --reporter=html
```
(Add `--ui` for interactive mode.)

## Maintenance Guidelines
1. Update `link_mapping.json` whenever a new Cortex view is created or an ERP route changes.
2. Re-run the Playwright suite after every deployment to catch broken links early.
3. Keep credentials in environment variables for CI/CD pipelines.

## Expected Outcome
- 100 % coverage of `Web.sitemap` URLs.
- Automated detection of 4xx/5xx responses in either application.
- Visual confirmation via screenshots.
- Clear mapping between legacy ERP pages and new Django Cortex endpoints.

